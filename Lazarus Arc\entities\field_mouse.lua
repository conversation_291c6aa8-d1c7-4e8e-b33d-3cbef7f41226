local FieldMouse = {
    id = "field_mouse",
    name = "Field Mouse",
    type = "field_mouse",
    shape = {
        {0, -1}, {1, 0}, {0, 1}, {-1, 0}
    },
    size = 3,

    -- Entity categories
    categories = {"animal", "rodent", "prey", "small"},

    -- Threat categories
    threatCategories = {"predator", "large"},

    -- Stats
    maxHealth = 3,
    health = 3,
    speed = 2.5, -- Slightly faster than regular mouse

    -- Behaviors
    behaviors = {"wander", "flee", "graze"},

    -- Behavior configurations
    behaviorConfigs = {
        wander = {
            moveSpeed = 1.8,
            changeDirectionChance = 0.15,
            idleChance = 0.2,
            idleDuration = {1, 2},
            wanderRadius = 12 -- Larger wander radius
        },
        flee = {
            useCategories = true,
            moveSpeed = 4.0, -- Faster than regular mouse
            detectionRadius = 7,
            useHiding = true
        },
        graze = {
            foodTypes = {"seeds", "berries", "grass"},
            foodValue = {
                seeds = 2,
                berries = 3,
                grass = 1
            },
            fleeWhenThreatened = true,
            grazeTime = {2, 5},
            moveTime = {3, 6},
            grazeRadius = 10
        }
    },

    -- Appearance
    appearance = {
        sprite = "field_mouse", -- Replace with your field mouse sprite
        scale = 0.6,
        animations = {
            "idle",
            "walk",
            "eat",
            "sniff",
            "panic"
        }
    },

    -- Sound effects
    sounds = {
        squeak = "mouse_squeak"
    },

    -- Loot drops
    drops = {}
}

-- Initialize the field mouse entity
function FieldMouse.init(entity, world)
    -- First, handle case where init is called during module registration
    -- by world_core.lua: module.init(WorldCore) or chunk_system.lua: module.init(module, world)
    if entity == FieldMouse and world then
        -- This is a module registration call, not an entity initialization call
        -- Can store WorldCore reference if needed
        FieldMouse.worldCore = world
        return FieldMouse
    end

    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors only if the world parameter includes a modules table
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    else
        print("Warning: FieldMouse.init did not receive a valid world with modules.")
    end

    return entity
end

return FieldMouse
