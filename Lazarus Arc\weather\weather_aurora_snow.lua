local AuroraWeather = require("weather.weather_aurora")

-- Create a new table that inherits from AuroraWeather
local AuroraSnowWeather = {}
for k, v in pairs(AuroraWeather) do
    AuroraSnowWeather[k] = v
end

-- Store references to parent methods
local parentInit = AuroraWeather.init
local parentCleanUp = AuroraWeather.cleanUp

-- Override the base aurora weather to include snow
AuroraSnowWeather.id = "aurora_snow"
AuroraSnowWeather.name = "Aurora Borealis with Snow"
AuroraSnowWeather.description = "A mesmerizing display of northern lights dancing across the night sky, accompanied by gentle snowfall"
AuroraSnowWeather.withSnow = true

-- Adjust environment for snow
AuroraSnowWeather.environment = {
    visibility = 0.7,      -- Slightly reduced visibility due to snow
    temperature = -15,     -- Colder with snow
    windSpeed = 0.3       -- Slightly stronger wind
}

-- Add snow-specific sounds
AuroraSnowWeather.sounds.snow = "weather/snow_ambient"

-- Override init to ensure snow is enabled
function AuroraSnowWeather.init(world)
    if not world then return false end
    
    -- Call parent init first
    local success = parentInit(world)
    if not success then return false end
    
    -- Ensure snow is enabled
    AuroraSnowWeather.withSnow = true
    
    -- Create snow particle system
    AuroraSnowWeather.particleSystems.snow = love.graphics.newParticleSystem(love.graphics.newCanvas(2, 2), 1000)
    AuroraSnowWeather.particleSystems.snow:setParticleLifetime(5, 10)
    AuroraSnowWeather.particleSystems.snow:setEmissionRate(100)
    AuroraSnowWeather.particleSystems.snow:setSizeVariation(0.3)
    AuroraSnowWeather.particleSystems.snow:setLinearAcceleration(-10, 20, 10, 40)
    AuroraSnowWeather.particleSystems.snow:setColors(1, 1, 1, 0.8, 1, 1, 1, 0)
    
    -- Start snow sound if weather system exists
    if world.weatherSystem and AuroraSnowWeather.sounds.snow then
        world.weatherSystem:playSound(AuroraSnowWeather.sounds.snow, {
            verbose = false,  -- Don't print warnings if sound is missing
            loop = true,      -- Snow sounds should loop
            volume = 0.3      -- Lower volume for snow
        })
    end
    
    -- Notify players if message system exists
    if world.messageSystem then
        world.messageSystem:broadcast("Snowflakes begin to fall as the aurora dances above.")
    else
        print("Snowflakes begin to fall as the aurora dances above.")
    end
    
    return true
end

-- Override cleanUp to handle snow-specific cleanup
function AuroraSnowWeather.cleanUp(world)
    if not world then return end
    
    -- Call parent cleanUp first
    parentCleanUp(world)
    
    -- Stop snow sound if weather system exists
    if world.weatherSystem and AuroraSnowWeather.sounds.snow then
        world.weatherSystem:stopSound(AuroraSnowWeather.sounds.snow)
    end
    
    -- Clean up snow particle system
    if AuroraSnowWeather.particleSystems.snow then
        AuroraSnowWeather.particleSystems.snow:stop()
    end
    
    -- Notify players if message system exists
    if world.messageSystem then
        world.messageSystem:broadcast("The snow stops falling as the aurora fades.")
    else
        print("The snow stops falling as the aurora fades.")
    end
end

return AuroraSnowWeather 