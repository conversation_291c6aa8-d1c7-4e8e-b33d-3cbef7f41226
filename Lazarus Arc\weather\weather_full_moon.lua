local FullMoonWeather = {
    id = "full_moon",
    name = "Full Moon",
    description = "A bright full moon illuminates the night, casting long shadows and enhancing magical energies",
    
    -- Visual properties
    visual = {
        skyColor = {r = 20, g = 20, b = 40},  -- Deep blue night sky
        moonColor = {r = 255, g = 240, b = 200}, -- Bright white moon
        ambientLightLevel = 0.4,
        moonIntensity = 1.0,
        shadowColor = {r = 0, g = 0, b = 0, a = 0.5}
    },
    
    -- Environment modifiers
    environment = {
        visibility = 0.8,      -- Enhanced night visibility
        temperature = 0.9,     -- Slightly cooler
        windSpeed = 0.2,       -- Light breeze
        shadowIntensity = 0.7  -- Strong shadows
    },
    
    -- Particle systems
    particleSystems = {
        moonbeams = nil,
        magic = nil
    },
    
    -- Sounds
    sounds = {
        ambient = "weather/full_moon_ambient",
        wind = "weather/moonlight_wind"
    },
    
    -- Effects on game entities and tiles
    effects = {
        -- Movement effects
        movementSpeedMultiplier = 1.1, -- Slightly increased speed
        
        -- Vision effects
        visionRange = 1.2, -- Enhanced night vision
        
        -- Temperature effects
        temperatureModifier = 0.9, -- Slightly cooler
        
        -- Energy effects
        energyDrainRate = 0.8, -- Reduced energy drain
        
        -- Special effects
        magicBoost = 1.3,    -- Enhanced magical abilities
        shadowPower = 1.2,   -- Enhanced shadow abilities
        transformationChance = 0.05 -- Chance of magical transformation
    },
    
    -- Day/night cycle modifiers
    timeModifiers = {
        dawn = {
            skyColor = {r = 30, g = 30, b = 50},
            ambientLightLevel = 0.3
        },
        day = {
            -- Uses default properties
        },
        dusk = {
            skyColor = {r = 25, g = 25, b = 45},
            ambientLightLevel = 0.5
        },
        night = {
            skyColor = {r = 15, g = 15, b = 35},
            ambientLightLevel = 0.6
        }
    }
}

-- Initialize the full moon weather
function FullMoonWeather.init(world)
    if not world then return false end
    
    -- Create moonbeam particle system
    FullMoonWeather.particleSystems.moonbeams = love.graphics.newParticleSystem(love.graphics.newCanvas(1, 1), 1000)
    FullMoonWeather.particleSystems.moonbeams:setParticleLifetime(3, 5)
    FullMoonWeather.particleSystems.moonbeams:setEmissionRate(40)
    FullMoonWeather.particleSystems.moonbeams:setSizeVariation(0.4)
    FullMoonWeather.particleSystems.moonbeams:setLinearAcceleration(0, 0, 0, 0)
    FullMoonWeather.particleSystems.moonbeams:setColors(1, 1, 1, 0.2, 1, 1, 1, 0)
    
    -- Create magic particle system
    FullMoonWeather.particleSystems.magic = love.graphics.newParticleSystem(love.graphics.newCanvas(1, 1), 1000)
    FullMoonWeather.particleSystems.magic:setParticleLifetime(2, 4)
    FullMoonWeather.particleSystems.magic:setEmissionRate(30)
    FullMoonWeather.particleSystems.magic:setSizeVariation(0.3)
    FullMoonWeather.particleSystems.magic:setLinearAcceleration(0, 0, 0, 0)
    FullMoonWeather.particleSystems.magic:setColors(0.8, 0.8, 1, 0.3, 0.8, 0.8, 1, 0)
    
    -- Start ambient sounds if weather system exists
    if world.weatherSystem then
        if FullMoonWeather.sounds.ambient then
            world.weatherSystem:playSound(FullMoonWeather.sounds.ambient, {
                verbose = false,  -- Don't print warnings if sound is missing
                loop = true,      -- Ambient sounds should loop
                volume = 0.4      -- Lower volume for ambient weather
            })
        end
        if FullMoonWeather.sounds.wind then
            world.weatherSystem:playSound(FullMoonWeather.sounds.wind, {
                verbose = false,  -- Don't print warnings if sound is missing
                loop = true,      -- Wind sounds should loop
                volume = 0.3      -- Lower volume for wind
            })
        end
    end
    
    -- Notify players if message system exists
    if world.messageSystem then
        world.messageSystem:broadcast("The full moon rises, casting its silvery light across the land.")
    else
        print("The full moon rises, casting its silvery light across the land.")
    end
    
    return true
end

-- Update full moon effects
function FullMoonWeather.update(world, dt)
    if not world then return end
    
    -- Update particle systems
    if FullMoonWeather.particleSystems.moonbeams then
        FullMoonWeather.particleSystems.moonbeams:update(dt)
    end
    if FullMoonWeather.particleSystems.magic then
        FullMoonWeather.particleSystems.magic:update(dt)
    end
    
    -- Apply environmental effects
    if world.weatherSystem then
        world.weatherSystem:applyEffects(FullMoonWeather.environment)
    end
    
    -- Apply effects to entities
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Apply magic boost
            if entity.magic then
                entity.magic = entity.magic * FullMoonWeather.effects.magicBoost
            end
            
            -- Apply shadow power boost
            if entity.shadowPower then
                entity.shadowPower = entity.shadowPower * FullMoonWeather.effects.shadowPower
            end
            
            -- Random transformation chance
            if math.random() < FullMoonWeather.effects.transformationChance * dt then
                -- Trigger magical transformation
                if world.effectSystem then
                    world.effectSystem:addEffect(entity, "moonlight_transformation", 3)
                end
            end
        end
    end
end

-- Draw the full moon effect
function FullMoonWeather.draw(horizonY)
    local width = love.graphics.getWidth()
    local height = love.graphics.getHeight()
    
    -- Draw moon
    love.graphics.setColor(FullMoonWeather.visual.moonColor.r/255, 
                         FullMoonWeather.visual.moonColor.g/255, 
                         FullMoonWeather.visual.moonColor.b/255, 
                         0.8)
    love.graphics.circle("fill", width/2, horizonY - 150, 50)
    
    -- Draw moonbeams
    if FullMoonWeather.particleSystems.moonbeams then
        love.graphics.setColor(1, 1, 1, 0.4)
        love.graphics.draw(FullMoonWeather.particleSystems.moonbeams, width/2, horizonY - 100)
    end
    
    -- Draw magic particles
    if FullMoonWeather.particleSystems.magic then
        love.graphics.setColor(0.8, 0.8, 1, 0.3)
        love.graphics.draw(FullMoonWeather.particleSystems.magic, width/2, horizonY - 50)
    end
end

-- Clean up when weather changes
function FullMoonWeather.cleanUp(world)
    if not world then return end
    
    -- Stop sounds if weather system exists
    if world.weatherSystem then
        if FullMoonWeather.sounds.ambient then
            world.weatherSystem:stopSound(FullMoonWeather.sounds.ambient)
        end
        if FullMoonWeather.sounds.wind then
            world.weatherSystem:stopSound(FullMoonWeather.sounds.wind)
        end
    end
    
    -- Clean up particle systems
    if FullMoonWeather.particleSystems.moonbeams then
        FullMoonWeather.particleSystems.moonbeams:stop()
    end
    if FullMoonWeather.particleSystems.magic then
        FullMoonWeather.particleSystems.magic:stop()
    end
    
    -- Notify players if message system exists
    if world.messageSystem then
        world.messageSystem:broadcast("The full moon sets, its magical influence fading.")
    else
        print("The full moon sets, its magical influence fading.")
    end
end

return FullMoonWeather 