local AuroraWeather = {
    id = "aurora",
    name = "Aurora Borealis",
    description = "A mesmerizing display of northern lights dancing across the night sky",
    visual = {
        skyColor = {r = 0, g = 50, b = 100},  -- Deep night blue
        auroraColors = {
            {r = 0, g = 255, b = 100},  -- Green
            {r = 0, g = 200, b = 255},  -- Blue
            {r = 200, g = 0, b = 255},  -- Purple
            {r = 255, g = 100, b = 0}   -- Red
        }
    },
    environment = {
        visibility = 0.8,
        temperature = -10,  -- Cold but not extreme
        windSpeed = 0.2    -- Gentle breeze
    },
    sounds = {
        ambient = "weather/aurora_ambient",
        wind = "weather/aurora_wind"
    },

    -- Synth orchestra sound configurations
    synthSounds = {
        ["weather/aurora_ambient"] = {
            notes = {261.6, 329.6, 392, 523.3}, -- C4, E4, G4, C5 - ethereal, mystical tones
            durations = {6.0, 6.0, 6.0, 6.0},
            instrument = "soft_pad",
            volume = 0.16,
            vibrato = true,
            vibratoRate = 0.12,
            vibratoDepth = 0.06
        },
        ["weather/aurora_wind"] = {
            notes = {174.6, 220, 261.6}, -- F3, A3, C4 - cold, mystical wind
            durations = {4.0, 4.0, 4.0},
            instrument = "flute",
            volume = 0.14,
            vibrato = true,
            vibratoRate = 0.25,
            vibratoDepth = 0.15
        }
    },
    particleSystems = {
        aurora = nil,
        snow = nil
    },
    auroraPatterns = {
        {frequency = 0.5, amplitude = 0.3, phase = 0},
        {frequency = 0.3, amplitude = 0.4, phase = math.pi/2},
        {frequency = 0.7, amplitude = 0.2, phase = math.pi}
    },
    time = 0
}

-- Initialize the aurora weather
function AuroraWeather.init(world)
    if not world then return false end
    
    -- Create aurora particle system
    AuroraWeather.particleSystems.aurora = love.graphics.newParticleSystem(love.graphics.newCanvas(1, 1), 1000)
    AuroraWeather.particleSystems.aurora:setParticleLifetime(2, 4)
    AuroraWeather.particleSystems.aurora:setEmissionRate(50)
    AuroraWeather.particleSystems.aurora:setSizeVariation(0.5)
    AuroraWeather.particleSystems.aurora:setLinearAcceleration(0, 0, 0, 0)
    AuroraWeather.particleSystems.aurora:setColors(1, 1, 1, 0.2, 1, 1, 1, 0)
    
    -- Create snow particle system if snow is enabled
    if AuroraWeather.withSnow then
        AuroraWeather.particleSystems.snow = love.graphics.newParticleSystem(love.graphics.newCanvas(2, 2), 1000)
        AuroraWeather.particleSystems.snow:setParticleLifetime(5, 10)
        AuroraWeather.particleSystems.snow:setEmissionRate(100)
        AuroraWeather.particleSystems.snow:setSizeVariation(0.3)
        AuroraWeather.particleSystems.snow:setLinearAcceleration(-10, 20, 10, 40)
        AuroraWeather.particleSystems.snow:setColors(1, 1, 1, 0.8, 1, 1, 1, 0)
    end
    
    -- Start ambient sounds if weather system exists
    if world.weatherSystem then
        if AuroraWeather.sounds.ambient then
            world.weatherSystem:playSound(AuroraWeather.sounds.ambient, {
                verbose = false,  -- Don't print warnings if sound is missing
                loop = true,      -- Ambient sounds should loop
                volume = 0.4      -- Lower volume for ambient weather
            })
        end
        if AuroraWeather.sounds.wind then
            world.weatherSystem:playSound(AuroraWeather.sounds.wind, {
                verbose = false,  -- Don't print warnings if sound is missing
                loop = true,      -- Wind sounds should loop
                volume = 0.3      -- Lower volume for wind
            })
        end
    end
    
    -- Notify players if message system exists
    if world.messageSystem then
        world.messageSystem:broadcast("The northern lights begin to dance across the sky.")
    else
        print("The northern lights begin to dance across the sky.")
    end
    
    return true
end

-- Update aurora patterns and particles
function AuroraWeather.update(world, dt)
    if not world then return end
    
    -- Update time for pattern animation
    AuroraWeather.time = AuroraWeather.time + dt * 0.1
    
    -- Update aurora particle system
    if AuroraWeather.particleSystems.aurora then
        AuroraWeather.particleSystems.aurora:update(dt)
    end
    
    -- Update snow particles if enabled
    if AuroraWeather.withSnow and AuroraWeather.particleSystems.snow then
        AuroraWeather.particleSystems.snow:update(dt)
    end
    
    -- Apply environmental effects
    if world.weatherSystem then
        world.weatherSystem:applyEffects(AuroraWeather.environment)
    end
end

-- Draw the aurora effect
function AuroraWeather.draw(horizonY)
    local width = love.graphics.getWidth()
    local height = love.graphics.getHeight()
    
    -- Draw aurora patterns
    for i, pattern in ipairs(AuroraWeather.auroraPatterns) do
        local color = AuroraWeather.visual.auroraColors[i % #AuroraWeather.visual.auroraColors + 1]
        local alpha = 0.3 + 0.2 * math.sin(AuroraWeather.time + pattern.phase)
        
        love.graphics.setColor(color.r/255, color.g/255, color.b/255, alpha)
        
        -- Draw aurora bands
        for y = horizonY - 100, horizonY - 20, 20 do
            local offset = pattern.amplitude * math.sin(AuroraWeather.time * pattern.frequency + y * 0.01)
            love.graphics.line(
                0, y + offset,
                width, y + offset
            )
        end
    end
    
    -- Draw aurora particles
    if AuroraWeather.particleSystems.aurora then
        love.graphics.setColor(1, 1, 1, 0.5)
        love.graphics.draw(AuroraWeather.particleSystems.aurora, 0, horizonY - 150)
    end
    
    -- Draw snow particles if enabled
    if AuroraWeather.withSnow and AuroraWeather.particleSystems.snow then
        love.graphics.setColor(1, 1, 1, 0.8)
        love.graphics.draw(AuroraWeather.particleSystems.snow, 0, 0)
    end
end

-- Clean up when weather changes
function AuroraWeather.cleanUp(world)
    if not world then return end
    
    -- Stop sounds if weather system exists
    if world.weatherSystem then
        if AuroraWeather.sounds.ambient then
            world.weatherSystem:stopSound(AuroraWeather.sounds.ambient)
        end
        if AuroraWeather.sounds.wind then
            world.weatherSystem:stopSound(AuroraWeather.sounds.wind)
        end
    end
    
    -- Clean up particle systems
    if AuroraWeather.particleSystems.aurora then
        AuroraWeather.particleSystems.aurora:stop()
    end
    if AuroraWeather.particleSystems.snow then
        AuroraWeather.particleSystems.snow:stop()
    end
    
    -- Notify players if message system exists
    if world.messageSystem then
        world.messageSystem:broadcast("The aurora fades from the sky.")
    else
        print("The aurora fades from the sky.")
    end
end

return AuroraWeather 